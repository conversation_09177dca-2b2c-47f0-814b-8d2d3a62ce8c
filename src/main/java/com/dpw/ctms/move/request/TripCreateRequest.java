package com.dpw.ctms.move.request;

import com.dpw.ctms.move.annotation.ValidEnum;
import com.dpw.ctms.move.enums.TripStatus;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TripCreateRequest {
    @NotBlank(message = "tripCode cannot be blank or null")
    @Schema(
            name = "code",
            description = "Trip's unique identifier",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    String code;

    @NotNull(message = "status cannot be null")
    @Schema(
            name = "status",
            description = "Trip status",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @ValidEnum(enumClass = TripStatus.class, message = "Invalid trip status. Allowed values: {enumValues}")
    String status;

    @NotBlank(message = "externalOriginLocationCode cannot be blank or null")
    @Schema(
            name = "externalOriginLocationCode",
            description = "origin facility id of trip",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    String externalOriginLocationCode;

    @NotBlank(message = "externalDestinationLocationCode cannot be blank or null")
    @Schema(
            name = "externalDestinationLocationCode",
            description = "destination facility id of the trip",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    String externalDestinationLocationCode;

    @NotNull(message = "expectedStartAt cannot be null")
    @Schema(
            name = "expectedStartAt",
            description = "expected start time of the trip",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    Long expectedStartAt;

    @NotNull(message = "expectedEndAt cannot be null")
    @Schema(
            name = "expectedEndAt",
            description = "expected end time of the trip",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    Long expectedEndAt;

    @Valid
    @NotNull(message = "stops in a trip cannot be null")
    @Schema(
            name = "stops",
            description = "list of stops within the trip",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    List<StopRequest> stops;

    @Valid
    @Schema(
            name = "shipments",
            description = "list of shipments going in the trip",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    List<ShipmentRequest> shipments;

    @Valid
    @Schema(
            name = "tasks",
            description = "tasks in a trip"
    )
    List<TaskRequest> tasks;

    @Valid
    @NotNull(message = "vehicle should be present for a trip")
    @Schema(
            name = "vehicleResourceRequest",
            description = "vehicle to be used in the trip",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    VehicleResourceRequest vehicleResource;

    @Valid
    @Schema(
            name = "trailerResourceRequests",
            description = "trailers to be used in the trip"
    )
    List<TrailerResourceRequest> trailerResources;

    @Valid
    @Schema(
            name = "vehicleOperatorResources",
            description = "vehicle operators to be used in the trip"
    )
    List<VehicleOperatorResourceRequest> vehicleOperatorResources;
    JsonNode details;  // Exact class to be used in place of JsonNode TBD
}
