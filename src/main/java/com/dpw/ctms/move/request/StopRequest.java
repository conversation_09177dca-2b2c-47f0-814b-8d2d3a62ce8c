package com.dpw.ctms.move.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StopRequest {
    @NotBlank(message = "externalLocationCode for a stop cannot be blank or null")
    @Schema(
            name = "externalLocationCode",
            description = "location id of the stop",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    String externalLocationCode;

    @NotNull(message = "sequence cannot be null")
    @Schema(
            name = "sequence",
            description = "sequence of the stop",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    Integer sequence;

    SegmentDetailsRequest segmentDetails;
}
