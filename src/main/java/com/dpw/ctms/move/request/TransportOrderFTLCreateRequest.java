package com.dpw.ctms.move.request;

import com.dpw.ctms.move.annotation.ValidEnum;
import com.dpw.ctms.move.enums.AssignmentType;
import com.dpw.ctms.move.enums.TransportOrderStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TransportOrderFTLCreateRequest {
    @NotBlank(message = "Transport order code should not be blank or null")
    @Schema(
            name = "code",
            description = "Uniquely identifies a transport order",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    String code;

    @NotNull(message = "Transport order status should not be null")
    @Schema(
            name = "status",
            description = "Transport order status",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @ValidEnum(enumClass = TransportOrderStatus.class, message = "Invalid transport order status. Allowed values: {enumValues}")
    String status;

    @NotNull(message = "Transport Order assignment type cannot be null")
    @Schema(
            name = "assignmentType",
            description = "Transport order assignment type",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    AssignmentType assignmentType;

    @Schema(
            name = "assignmentCode",
            description = "Transport order assignment code"
    )
    String assignmentCode;

    @Schema(
            name = "assigneeIdentifier",
            description = "Transport order assignee identifier"
    )
    String assigneeIdentifier;

    @Valid
    @Schema(
            name = "trips",
            description = "List of trips in a transport order"
    )
    List<TripCreateRequest> trips;
}
