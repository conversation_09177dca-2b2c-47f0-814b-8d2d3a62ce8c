package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.repository.ShipmentRepository;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;
import com.dpw.ctms.move.service.IShipmentService;
import com.dpw.ctms.move.service.ShipmentFilteringService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class ShipmentService implements IShipmentService {

    private final ShipmentRepository shipmentRepository;
    private final ShipmentFilteringService shipmentFilteringService;

    @Override
    public ListResponse<ShipmentListingResponse> listShipments(ShipmentListingRequest shipmentListingRequest) {
        return shipmentFilteringService.filterShipments(shipmentListingRequest);
    }

    @Override
    public List<Shipment> getAllByCodes(Set<String> codes){
        return shipmentRepository.findAllByCodeIn(codes);
    }

}
