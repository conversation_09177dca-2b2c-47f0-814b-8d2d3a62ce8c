package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.Shipment;

import java.util.List;
import java.util.Set;
import com.dpw.ctms.move.request.ShipmentListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.ShipmentListingResponse;

public interface IShipmentService {
    ListResponse<ShipmentListingResponse> listShipments(ShipmentListingRequest shipmentListingRequest);
    List<Shipment> getAllByCodes(Set<String> codes);
}
