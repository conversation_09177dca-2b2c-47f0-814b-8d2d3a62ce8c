package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.mapper.IEntityRelationshipMapper;
import com.dpw.ctms.move.mapper.TransportOrderMapper;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.ctms.move.response.TransportOrderResponse;
import com.dpw.ctms.move.service.ITransportOrderService;
import com.dpw.ctms.move.service.TransportOrderFilteringService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.dpw.ctms.move.constants.ResponseMessageConstants.TRANSPORT_ORDER_CREATION_SUCCESS_MESSAGE;

@Service
@RequiredArgsConstructor
@Slf4j
public class TransportOrderServiceImpl implements ITransportOrderService {
    private final TransportOrderMapper transportOrderMapper;
    private final TransportOrderRepository transportOrderRepository;
    private final List<IEntityRelationshipMapper> relationshipMappers;
    private final TransportOrderFilteringService transportOrderFilteringService;

    @Override
    @Transactional
    public TransportOrderResponse createTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest) {
        TransportOrderDTO transportOrderDTO = transportOrderMapper.toDTO(transportOrderFTLCreateRequest);
        TransportOrder transportOrder = transportOrderMapper.toEntity(transportOrderDTO);
        processTransportOrder(transportOrderDTO,transportOrder);
        TransportOrder persistedTransportOrder =  saveTransportOrder(transportOrder);
        return buildTransportOrderResponse(persistedTransportOrder);
    }

    @Override
    public ListResponse<TransportOrderListingResponse> listTransportOrders(TransportOrderListingRequest transportOrderListingRequest) {
        return transportOrderFilteringService.filterTransportOrders(
                transportOrderListingRequest
        );
    }

    private TransportOrderResponse buildTransportOrderResponse(TransportOrder transportOrder) {
        return TransportOrderResponse.builder()
                .transportOrderCode(transportOrder.getCode())
                .isSuccess(true)
                .message(TRANSPORT_ORDER_CREATION_SUCCESS_MESSAGE)
                .build();
    }

    private void processTransportOrder(TransportOrderDTO transportOrderDTO, TransportOrder transportOrder) {
        for (IEntityRelationshipMapper entityRelationshipMapper : relationshipMappers) {
            entityRelationshipMapper.mapRelationships(transportOrderDTO, transportOrder);
        }
    }

    @Override
    public TransportOrder saveTransportOrder(TransportOrder transportOrder) {
        TransportOrder persistedTransportOrder = transportOrderRepository.save(transportOrder);
        log.info("Transport Order and its related entities successfully persisted: {}", persistedTransportOrder);
        return persistedTransportOrder;
    }

}
