package com.dpw.ctms.move.service.impl;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TaskParam;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.service.ITaskParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class TaskParamService implements ITaskParamService {

    public Optional<ParamValueShipmentDTO> getShipmentCode(Task task) {
        return task.getTaskParams().stream()
                .filter(param -> TaskParamType.SHIPMENT.equals(param.getParamName()))
                .map(TaskParam::getParamValue)
                .filter(ParamValueShipmentDTO.class::isInstance)
                .map(ParamValueShipmentDTO.class::cast)
                .findFirst();
    }
}
