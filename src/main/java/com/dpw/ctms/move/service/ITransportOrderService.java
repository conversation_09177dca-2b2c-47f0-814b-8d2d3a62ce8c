package com.dpw.ctms.move.service;

import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.request.TransportOrderListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TransportOrderListingResponse;
import com.dpw.ctms.move.response.TransportOrderResponse;

public interface ITransportOrderService {
    TransportOrderResponse createTransportOrderFTLFulfilment(TransportOrderFTLCreateRequest transportOrderFTLCreateRequest);
    TransportOrder saveTransportOrder(TransportOrder transportOrder);
    ListResponse<TransportOrderListingResponse> listTransportOrders(TransportOrderListingRequest transportOrderListingRequest);

}
