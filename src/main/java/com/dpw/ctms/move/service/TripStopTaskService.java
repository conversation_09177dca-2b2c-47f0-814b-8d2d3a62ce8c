package com.dpw.ctms.move.service;

import com.dpw.ctms.move.request.FilterRequest;
import com.dpw.ctms.move.request.TripTaskListingRequest;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripFacilityTasksDetailsResponse;
import org.springframework.transaction.annotation.Transactional;

public interface TripStopTaskService {

    @Transactional
    ListResponse<TripFacilityTasksDetailsResponse> getTripTasks(
            String tripCode, FilterRequest<TripTaskListingRequest> tripTaskListingDTO);
}
