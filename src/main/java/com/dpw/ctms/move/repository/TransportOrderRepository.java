package com.dpw.ctms.move.repository;

import com.dpw.ctms.move.entity.TransportOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


@Repository
public interface TransportOrderRepository extends JpaRepository<TransportOrder, Long>, JpaSpecificationExecutor<TransportOrder> {

    @EntityGraph(value = "TransportOrder.trips", type = EntityGraph.EntityGraphType.LOAD)
    Page<TransportOrder> findAll(Specification<TransportOrder> spec, Pageable pageable);
}
