package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.TripDTO;
import com.dpw.ctms.move.entity.*;
import org.mapstruct.*;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {ShipmentMapper.class, TaskMapper.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TripMapper {

    Trip toEntity(TripDTO tripDTO);

    @AfterMapping
    default void setBackReference(@MappingTarget Trip trip) {
        if (trip.getShipments() != null) {
            for (Shipment shipment : trip.getShipments()) {
                shipment.setTrip(trip);
            }
        }
        if (trip.getStops() != null) {
            for (Stop stop : trip.getStops()) {
                stop.setTrip(trip);
            }
        }
        /* vehicle resource cannot be null. TripRequest ensures that */
        trip.getVehicleResource().setTrip(trip);

        if (trip.getTrailerResources() != null) {
            for (TrailerResource trailerResource : trip.getTrailerResources()) {
                trailerResource.setTrip(trip);
            }
        }

        if (trip.getVehicleOperatorResources() != null) {
            for (VehicleOperatorResource vehicleOperatorResource : trip.getVehicleOperatorResources()) {
                vehicleOperatorResource.setTrip(trip);
            }
        }
    }

}
