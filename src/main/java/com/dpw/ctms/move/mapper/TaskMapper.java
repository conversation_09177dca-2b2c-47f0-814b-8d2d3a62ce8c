package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.TaskDTO;
import com.dpw.ctms.move.response.TripTasksDetailsResponse;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.enums.DisplayableStatusEnum;
import com.dpw.ctms.move.request.TaskRequest;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import org.mapstruct.*;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {TaskParamMapper.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TaskMapper {

    TaskDTO toDTO(TaskRequest taskRequest);

    Task toEntity(TaskDTO taskDTO);

    /**
     * Maps a single Task entity to TripTasksDetailsDTO
     * @param task - The Task entity to map
     * @return TripTasksDetailsDTO - The mapped DTO
     */
    @Mappings({
            @Mapping(target = "externalTaskRegistrationCode", source = "task.externalTaskRegistrationCode"),
            @Mapping(target = "sequence", expression = "java(task.getSequence() != null ? task.getSequence() : null)"),
            @Mapping(target = "status", source = "task.status", qualifiedByName = "mapStatusInfo"),
            @Mapping(target = "externalTaskMasterCode", source = "task.externalTaskMasterCode"),
            @Mapping(target = "expectedStartAt", source = "task.expectedStartAt"),
            @Mapping(target = "expectedEndAt", source = "task.expectedEndAt"),
            @Mapping(target = "actualStartAt", source = "task.actualStartAt"),
            @Mapping(target = "actualEndAt", source = "task.actualEndAt"),
            @Mapping(target = "updatedAt", source = "task.updatedAt"),
            @Mapping(target = "externalConsignmentId", source = "consignmentCode"),
            @Mapping(target = "externalCustomerOrderId", source = "customerOrderId"),
            @Mapping(target = "shipmentCode", source = "shipmentCode")
    })
    TripTasksDetailsResponse toDTO(Task task, String shipmentCode, String consignmentCode, String customerOrderId);

    /**
     * Creates a StatusInfo object from any enum that implements DisplayableStatusEnum.
     * @param statusEnum The enum instance (e.g., TripStatus.PLANNED, AssignmentType.INTERNAL)
     * @return A StatusInfo object with the enum's display representation as label and its name() as value, or null if statusEnum is null.
     */
    @Named("mapStatusInfo")
    default EnumLabelValueResponse mapStatusInfo(DisplayableStatusEnum statusEnum) {
        if (statusEnum == null) {
            return null;
        }
        return new EnumLabelValueResponse(statusEnum.getDisplayName(), statusEnum.name());
    }
}