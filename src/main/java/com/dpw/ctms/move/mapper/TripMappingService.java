package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.DisplayableStatusEnum;
import com.dpw.ctms.move.response.EnumLabelValueResponse;
import com.dpw.ctms.move.response.TripListingResponse;
import org.mapstruct.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * MapStruct mapper for mapping Trip entities to TripListingResponse DTOs
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TripMappingService {

    @Mapping(target = "code", source = "code")
    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatusInfo")
    @Mapping(target = "transportOrderCode", source = "transportOrder.code")
    @Mapping(target = "customerOrders", source = "shipments", qualifiedByName = "mapCustomerOrders")
    @Mapping(target = "assignmentDetails", source = ".", qualifiedByName = "mapAssignmentDetails")
    @Mapping(target = "resources", source = ".", qualifiedByName = "mapResources")
    @Mapping(target = "locations", source = ".", qualifiedByName = "mapLocations")
    @Mapping(target = "expectedTimes", source = ".", qualifiedByName = "mapTripExpectedTimes")
    @Mapping(target = "actualTimes", source = ".", qualifiedByName = "mapTripActualTimes")
    @Mapping(target = "updatedAt", source = "updatedAt")
    TripListingResponse mapToResponse(Trip trip);

    /**
     * Creates a StatusInfo object from any enum that implements DisplayableStatusEnum.
     *
     * @param statusEnum The enum instance (e.g., TripStatus.PLANNED, AssignmentType.INTERNAL)
     * @return A StatusInfo object with the enum's display representation as label and its name() as value, or null if statusEnum is null.
     */
    @Named("mapStatusInfo")
    default EnumLabelValueResponse mapStatusInfo(DisplayableStatusEnum statusEnum) {
        if (statusEnum == null) {
            return null;
        }
        return new EnumLabelValueResponse(statusEnum.getDisplayName(), statusEnum.name());
    }

    @Named("mapCustomerOrders")
    default List<TripListingResponse.CustomerOrder> mapCustomerOrders(Set<Shipment> shipments) {
        if (CollectionUtils.isEmpty(shipments)) {
            return new ArrayList<>();
        }

        return shipments.stream()
                .filter(shipment -> StringUtils.hasText(shipment.getExternalCustomerOrderId()))
                .map(shipment -> {
                    TripListingResponse.CustomerOrder customerOrder = new TripListingResponse.CustomerOrder();
                    customerOrder.setId(shipment.getExternalCustomerOrderId());
                    customerOrder.setUpdatedAt(shipment.getUpdatedAt());
                    return customerOrder;
                })
                .distinct()
                .collect(Collectors.toList());
    }

    @Named("mapAssignmentDetails")
    default TripListingResponse.AssignmentDetails mapAssignmentDetails(Trip trip) {
        TripListingResponse.AssignmentDetails assignmentDetails = new TripListingResponse.AssignmentDetails();

        if (trip.getTransportOrder() != null && trip.getTransportOrder().getAssignmentType() != null) {
            assignmentDetails.setType(mapStatusInfo(trip.getTransportOrder().getAssignmentType()));
        }

        // Vendor information from transport order
        if (trip.getTransportOrder() != null) {
            TripListingResponse.Vendor vendor = new TripListingResponse.Vendor();
            vendor.setCode(trip.getTransportOrder().getAssigneeIdentifier());
            assignmentDetails.setVendor(vendor);
            assignmentDetails.setUpdatedAt(trip.getTransportOrder().getUpdatedAt());
        }

        return assignmentDetails;
    }

    @Named("mapResources")
    default TripListingResponse.Resources mapResources(Trip trip) {
        TripListingResponse.Resources resources = new TripListingResponse.Resources();

        // Map vehicles - using consistent Stream API approach
        List<TripListingResponse.Vehicle> vehicles = trip.getVehicleResource() != null
                ? List.of(trip.getVehicleResource()).stream()
                .map(vehicleResource -> {
                    TripListingResponse.Vehicle vehicle = new TripListingResponse.Vehicle();
                    vehicle.setId(vehicleResource.getExternalResourceId());
                    vehicle.setTypeCode(vehicleResource.getExternalVehicleTypeId());
                    vehicle.setRegistrationNumber(vehicleResource.getRegistrationNumber());
                    vehicle.setUpdatedAt(vehicleResource.getUpdatedAt());
                    return vehicle;
                })
                .collect(Collectors.toList())
                : new ArrayList<>();
        resources.setVehicles(vehicles);

        // Map trailers - using consistent null-safe Stream API
        List<TripListingResponse.Trailer> trailers = CollectionUtils.isEmpty(trip.getTrailerResources())
                ? new ArrayList<>()
                : trip.getTrailerResources().stream()
                .map(trailerResource -> {
                    TripListingResponse.Trailer trailer = new TripListingResponse.Trailer();
                    trailer.setId(trailerResource.getExternalResourceId());
                    trailer.setUpdatedAt(trailerResource.getUpdatedAt());
                    return trailer;
                })
                .collect(Collectors.toList());
        resources.setTrailers(trailers);

        // Map vehicle operators - using consistent Stream API
        List<TripListingResponse.VehicleOperator> vehicleOperators = CollectionUtils.isEmpty(trip.getVehicleOperatorResources())
                ? new ArrayList<>()
                : trip.getVehicleOperatorResources().stream()
                .map(operatorResource -> {
                    TripListingResponse.VehicleOperator operator = new TripListingResponse.VehicleOperator();
                    operator.setId(operatorResource.getExternalResourceId());
                    // Map status from resource assignment details
                    if (operatorResource.getResourceAssignmentDetails() != null &&
                            operatorResource.getResourceAssignmentDetails().getAssignmentStatus() != null) {
                        operator.setStatus(mapStatusInfo(operatorResource.getResourceAssignmentDetails().getAssignmentStatus()));
                    }
                    operator.setUpdatedAt(operatorResource.getUpdatedAt());
                    return operator;
                })
                .collect(Collectors.toList());
        resources.setVehicleOperators(vehicleOperators);

        return resources;
    }

    @Named("mapLocations")
    default TripListingResponse.Locations mapLocations(Trip trip) {
        TripListingResponse.Locations locations = new TripListingResponse.Locations();

        // Origin
        TripListingResponse.LocationDetails origin = new TripListingResponse.LocationDetails();
        origin.setCode(trip.getExternalOriginLocationCode());
        locations.setOrigin(origin);

        // Destination
        TripListingResponse.LocationDetails destination = new TripListingResponse.LocationDetails();
        destination.setCode(trip.getExternalDestinationLocationCode());
        locations.setDestination(destination);

        return locations;
    }

    @Named("mapTripExpectedTimes")
    default TripListingResponse.TimeRange mapTripExpectedTimes(Trip trip) {
        TripListingResponse.TimeRange timeRange = new TripListingResponse.TimeRange();
        timeRange.setStartAt(trip.getExpectedStartAt());
        timeRange.setEndAt(trip.getExpectedEndAt());
        return timeRange;
    }

    @Named("mapTripActualTimes")
    default TripListingResponse.TimeRange mapTripActualTimes(Trip trip) {
        TripListingResponse.TimeRange timeRange = new TripListingResponse.TimeRange();
        timeRange.setStartAt(trip.getActualStartAt());
        timeRange.setEndAt(trip.getActualEndAt());
        return timeRange;
    }

}

