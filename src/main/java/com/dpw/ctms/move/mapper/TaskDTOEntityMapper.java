package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.TaskParamDTO;
import com.dpw.ctms.move.dto.TaskDTO;
import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.dto.TripDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TaskParam;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.registry.EntityTaskMappingStrategyRegistry;
import com.dpw.ctms.move.strategy.entitytaskmapping.IEntityTaskMappingStrategy;
import com.dpw.ctms.move.util.EntityFinderUtil;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;


import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TRIP_CODE;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Component
@RequiredArgsConstructor
public class TaskDTOEntityMapper implements IEntityRelationshipMapper{
    private final EntityFinderUtil entityFinderUtil;
    private final TaskMapper taskMapper;
    private final EntityTaskMappingStrategyRegistry entityTaskMappingStrategyRegistry;

    @Override
    public void mapRelationships(TransportOrderDTO transportOrderDTO, TransportOrder transportOrder) {
        if (transportOrderDTO.getTrips() == null) return;

        for(TripDTO tripDTO: transportOrderDTO.getTrips()) {
            Trip trip = entityFinderUtil.getEntityByCode(transportOrder.getTrips().stream().toList(),
                    tripDTO.getCode(), Trip::getCode,
                    ()-> new TMSException(
                            INVALID_REQUEST.name(),
                            String.format(INVALID_TRIP_CODE, tripDTO.getCode())
                    ));
            if (tripDTO.getTasks() != null) {
                for(TaskDTO taskDTO : tripDTO.getTasks()) {
                    Task task = createTask(taskDTO);
                    if (taskDTO.getTaskParams() == null) continue;

                    for (TaskParamDTO taskParamDTO: taskDTO.getTaskParams()) {
                        IEntityTaskMappingStrategy entityTaskMappingStrategy =
                                entityTaskMappingStrategyRegistry.getEntityTaskMappingStrategy(taskParamDTO.getParamName());
                        if (entityTaskMappingStrategy != null) {
                            entityTaskMappingStrategy.mapEntityTask(taskParamDTO.getParamValue(),trip,task);
                        }
                    }
                }
            }
        }
    }

    private Task createTask(TaskDTO taskDTO) {
        Task task = taskMapper.toEntity(taskDTO);
        for (TaskParam taskParam: task.getTaskParams()) {
            taskParam.setTask(task);
        }
        return task;
    }
}
