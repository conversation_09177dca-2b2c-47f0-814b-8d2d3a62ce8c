package com.dpw.ctms.move.mapper;

import com.dpw.ctms.move.dto.ShipmentDTO;
import com.dpw.ctms.move.dto.StopDetailsDTO;
import com.dpw.ctms.move.dto.TransportOrderDTO;
import com.dpw.ctms.move.dto.TripDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.TransportOrder;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.util.EntityFinderUtil;
import com.dpw.tmsutils.exception.TMSException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_SHIPMENT_CODE;
import static com.dpw.ctms.move.constants.ErrorMessageConstant.INVALID_TRIP_CODE;
import static com.dpw.tmsutils.utils.TMSExceptionErrorCode.INVALID_REQUEST;

@Component
@RequiredArgsConstructor
public class ShipmentStopMapper implements IEntityRelationshipMapper {
    private final EntityFinderUtil entityFinderUtil;

    @Override
    public void mapRelationships(TransportOrderDTO transportOrderDTO, TransportOrder transportOrder) {
        if (transportOrderDTO.getTrips() == null) return;

        for (TripDTO tripDTO : transportOrderDTO.getTrips()) {
            if (tripDTO.getShipments() != null) {
                Trip trip = entityFinderUtil.getEntityByCode(transportOrder.getTrips().stream().toList(),
                        tripDTO.getCode(), Trip::getCode,
                        ()-> new TMSException(
                                INVALID_REQUEST.name(),
                                String.format(INVALID_TRIP_CODE, tripDTO.getCode())
                        ));
                mapShipmentStops(tripDTO, trip);
            }
        }
    }

    private void mapShipmentStops(TripDTO tripDTO, Trip trip) {
        List<Stop> tripStops = Optional.ofNullable(trip.getStops())
                .orElse(Collections.emptySet())
                .stream()
                .toList();

        for (ShipmentDTO shipmentDTO : tripDTO.getShipments()) {
            Shipment shipment = entityFinderUtil.getEntityByCode(trip.getShipments().stream().toList(),
                    shipmentDTO.getCode(), Shipment::getCode,
                    () -> new TMSException(
                            INVALID_REQUEST.name(),
                            String.format(INVALID_SHIPMENT_CODE, shipmentDTO.getCode())
                    ));
            mapOriginAndDestinationStops(shipmentDTO, shipment, tripStops);
        }
    }

    private void mapOriginAndDestinationStops(ShipmentDTO shipmentDTO, Shipment shipment, List<Stop> tripStops) {
        StopDetailsDTO originStop = shipmentDTO.getOriginStop();
        StopDetailsDTO destinationStop = shipmentDTO.getDestinationStop();

        for (Stop stop : tripStops) {
            if (isOriginStop(originStop, stop)) {
                shipment.setOriginStop(stop);
                stop.getDepartureShipments().add(shipment);
            }

            if (isDestinationStop(destinationStop, stop)) {
                shipment.setDestinationStop(stop);
                stop.getArrivalShipments().add(shipment);
            }
        }
    }

    private boolean isOriginStop(StopDetailsDTO originStop, Stop stop) {
        return originStop != null &&
                stop.getExternalLocationCode().equals(originStop.getExternalLocationCode()) &&
                stop.getSequence().equals(originStop.getSequence());
    }

    private boolean isDestinationStop(StopDetailsDTO destinationStop, Stop stop) {
        return destinationStop != null &&
                stop.getExternalLocationCode().equals(destinationStop.getExternalLocationCode()) &&
                stop.getSequence().equals(destinationStop.getSequence());
    }
}