package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.dto.ParamValueBaseDTO;
import com.dpw.ctms.move.enums.TaskParamType;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

/**
 * TODO Have paramValue as code for any type of param (STOP, SHIPMENT etc and other details can go as part of details json
 **/
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "task_param")
@EqualsAndHashCode(callSuper = true)
@Audited
public class TaskParam extends BaseEntity{
    @Column(name = "param_name")
    @Enumerated(EnumType.STRING)
    private TaskParamType paramName;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "param_value", columnDefinition = "jsonb")
    private ParamValueBaseDTO paramValue;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id", referencedColumnName = "id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private Task task;
}
