package com.dpw.ctms.move.entity;

import com.dpw.ctms.move.enums.TripStatus;
import com.fasterxml.jackson.databind.JsonNode;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.Audited;
import org.hibernate.type.SqlTypes;

import java.util.Set;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "trip")
@EqualsAndHashCode(callSuper = true)
@Audited
public class Trip extends BaseEntity {

    @Column(name = "code", unique = true)
    private String code;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private TripStatus status;

    @Column(name = "external_origin_location_code")
    private String externalOriginLocationCode;

    @Column(name = "external_destination_location_code")
    private String externalDestinationLocationCode;

    @Column(name = "expected_start_at")
    private Long expectedStartAt;

    @Column(name = "expected_end_at")
    private Long expectedEndAt;

    @Column(name = "actual_start_at")
    private Long actualStartAt;

    @Column(name = "actual_end_at")
    private Long actualEndAt;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "details", columnDefinition = "jsonb")
    private JsonNode details;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    private Set<Shipment> shipments;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    private Set<Stop> stops;

    @OneToOne(mappedBy = "trip", cascade = CascadeType.ALL)
    private VehicleResource vehicleResource;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    private Set<TrailerResource> trailerResources;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    private Set<VehicleOperatorResource> vehicleOperatorResources;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transport_order_id")
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    private TransportOrder transportOrder;

    @OneToMany(mappedBy = "trip", cascade = CascadeType.ALL)
    private Set<Exception> exceptions;
}