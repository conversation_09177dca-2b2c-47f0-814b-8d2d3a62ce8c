package com.dpw.ctms.move.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
@Builder
public class TripTasksDetailsResponse {
    private String code;
    private String externalTaskRegistrationCode;
    private String externalTaskMasterCode;
    private Integer sequence;
    private EnumLabelValueResponse status;
    private String externalConsignmentId;
    private String externalCustomerOrderId;
    private String shipmentCode;
    private Long expectedStartAt;
    private Long expectedEndAt;
    private Long actualStartAt;
    private Long actualEndAt;
    private Long updatedAt;
}
