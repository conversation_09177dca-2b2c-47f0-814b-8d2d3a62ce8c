package com.dpw.ctms.move.response;

import com.dpw.ctms.move.dto.LocationDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TripFacilityTasksDetailsResponse {
    private LocationDetail locationDetails;
    private List<TripTasksDetailsResponse> locationTaskDetailsList;
}
