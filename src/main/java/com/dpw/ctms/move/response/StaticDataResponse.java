package com.dpw.ctms.move.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * This response provides a standardized format for returning enum values for entities
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaticDataResponse {

    private Map<String, EntityStaticDataResponse> data;

    private List<String> successfulEntityTypes;

    private List<EntityTypeError> failedEntityTypes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class EntityTypeError {

        private String entityType;

        private String errorCode;

        private String errorMessage;
    }
}
