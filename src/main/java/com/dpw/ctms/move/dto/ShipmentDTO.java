package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.enums.ShipmentStatus;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShipmentDTO {
    private String code;
    private StopDetailsDTO originStop;
    private StopDetailsDTO destinationStop;
    private Long expectedPickupAt;
    private Long expectedDeliveryAt;
    private String externalConsignmentId;
    private String externalCustomerOrderId;
    private ShipmentStatus status;
    private BigDecimal weight;
    private String weightUom;
    private BigDecimal volume;
    private String volumeUom;
    private JsonNode loadingDetails;
    private JsonNode unloadingDetails;
}
