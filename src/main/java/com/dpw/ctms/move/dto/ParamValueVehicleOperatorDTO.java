package com.dpw.ctms.move.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ParamValueVehicleOperatorDTO implements ParamValueBaseDTO {
    @NotBlank(message = "code in vehicle operator param value cannot be null")
    String code;
}
