package com.dpw.ctms.move.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationDetail {
    @NotBlank(message = "Location Code cannot be blank")
    @Schema(name = "externalLocationCode", description = "Location Code")
    private String externalLocationCode;
    @Schema(name = "sequence", description = "Sequence number of the stop")
    private Integer sequence;
    @Schema(name = "code", description = "Internal stop code")
    private String code;
    @Schema(name = "updatedAt", description = "stop updatedAt")
    private Long updatedAt;
}
