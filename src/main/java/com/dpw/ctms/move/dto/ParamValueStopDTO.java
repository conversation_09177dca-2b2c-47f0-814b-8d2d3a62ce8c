package com.dpw.ctms.move.dto;

import com.dpw.ctms.move.annotation.ValidEnum;
import com.dpw.ctms.move.enums.StopTaskEvent;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ParamValueStopDTO implements ParamValueBaseDTO {
    @NotBlank(message = "externalLocationCode cannot be null or empty")
    String externalLocationCode;

    @NotNull(message = "sequence cannot be null")
    Integer sequence;

    @NotNull(message = "Stop task event cannot be null")
    @ValidEnum(enumClass = StopTaskEvent.class, message = "Invalid stop task event. Allowed values: {enumValues}")
    String taskEvent;
}
