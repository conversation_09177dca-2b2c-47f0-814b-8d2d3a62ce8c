package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.repository.StopRepository;
import com.dpw.ctms.move.repository.TransportOrderRepository;
import com.dpw.ctms.move.repository.TripRepository;
import com.dpw.ctms.move.request.TransportOrderFTLCreateRequest;
import com.dpw.ctms.move.response.TransportOrderResponse;
import com.dpw.ctms.move.utils.Faker;
import com.dpw.tmsutils.threadlocal.TenantContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import static com.dpw.tmsutils.constant.RequestConstants.TENANT_HEADER;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class TransportOrderIntegrationTest extends IntegrationTestBase {
    @Autowired
    TransportOrderRepository transportOrderRepository;

    @Autowired
    StopRepository stopRepository;

    @Autowired
    TripRepository tripRepository;

    @AfterEach
    void cleanup() {
        TenantContext.setCurrentTenant("CFR");
        transportOrderRepository.deleteAll();
    }

    @Test
    public void test_transportOrder_with_trips_creation() throws Exception {
        TransportOrderFTLCreateRequest transportOrderFTLCreateRequest =
                Faker.createDummyTransportOrderCreateRequest();

        String requestBody = objectMapper.writeValueAsString(transportOrderFTLCreateRequest);

        MvcResult mvcResult = mockMvc.perform(post("/v1/transport-orders/ftl-fulfilment")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(TENANT_HEADER, "CFR")
                        .content(requestBody))
                .andExpect(status().isCreated())
                .andReturn();

        String responseJson = mvcResult.getResponse().getContentAsString();
        TransportOrderResponse transportOrderResponse = objectMapper.readValue(responseJson,
                TransportOrderResponse.class);
        Assertions.assertEquals(true, transportOrderResponse.getIsSuccess());
        Assertions.assertEquals(transportOrderFTLCreateRequest.getCode(), transportOrderResponse.getTransportOrderCode());
    }
}
