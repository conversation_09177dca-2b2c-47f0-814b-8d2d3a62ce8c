package com.dpw.ctms.move.controller;

import com.dpw.ctms.move.controller.impl.TripTaskApiController;
import com.dpw.ctms.move.dto.LocationDetail;
import com.dpw.ctms.move.response.TripTasksDetailsResponse;
import com.dpw.ctms.move.request.FilterRequest;
import com.dpw.ctms.move.request.TripTaskListingRequest;
import com.dpw.ctms.move.request.common.Pagination;
import com.dpw.ctms.move.response.ListResponse;
import com.dpw.ctms.move.response.TripFacilityTasksDetailsResponse;
import com.dpw.ctms.move.service.TripStopTaskService;
import com.dpw.tmsutils.exception.TMSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TripTaskApiControllerTest {

    @Mock
    private TripStopTaskService tripStopTaskService;

    @InjectMocks
    private TripTaskApiController tripTaskApiController;

    private String tripCode;
    private FilterRequest<TripTaskListingRequest> filterRequest;
    private TripTaskListingRequest tripTaskListingRequest;
    private Pagination pagination;
    private ListResponse<TripFacilityTasksDetailsResponse> mockServiceResponse;
    private List<TripFacilityTasksDetailsResponse> mockResponseList;

    @BeforeEach
    void setUp() {
        tripCode = "TRIP001";

        // Setup pagination
        pagination = new Pagination();
        pagination.setPageNo(0);
        pagination.setPageSize(10);

        // Setup request objects
        tripTaskListingRequest = new TripTaskListingRequest();
        filterRequest = new FilterRequest<>();
        filterRequest.setPagination(pagination);
        filterRequest.setFilters(tripTaskListingRequest);

        // Setup mock task DTOs
        TripTasksDetailsResponse taskDTO1 = new TripTasksDetailsResponse();
        taskDTO1.setCode("TASK001");

        TripTasksDetailsResponse taskDTO2 = new TripTasksDetailsResponse();
        taskDTO2.setCode("TASK002");

        // Setup mock facility responses
        TripFacilityTasksDetailsResponse facility1 = TripFacilityTasksDetailsResponse.builder()
                .locationDetails(LocationDetail.builder()
                        .externalLocationCode("EXT001")
                        .build())
                .locationTaskDetailsList(Arrays.asList(taskDTO1, taskDTO2))
                .build();

        TripFacilityTasksDetailsResponse facility2 = TripFacilityTasksDetailsResponse.builder()
                .locationDetails(LocationDetail.builder()
                        .externalLocationCode("EXT002")
                        .build())
                .locationTaskDetailsList(Arrays.asList(taskDTO1))
                .build();

        mockResponseList = Arrays.asList(facility1, facility2);

        // Setup mock service response
        mockServiceResponse = ListResponse.<TripFacilityTasksDetailsResponse>builder()
                .data(mockResponseList)
                .totalRecords(25L)
                .build();
    }

    @Test
    void getTripTasksByFacility_WithValidRequest_ShouldReturnOkResponseWithData() {
        // Given
        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenReturn(mockServiceResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals(mockServiceResponse, result.getBody());
        assertEquals(25, result.getBody().getTotalRecords());
        assertEquals(2, result.getBody().getData().size());

        // Verify first facility
        TripFacilityTasksDetailsResponse firstFacility = result.getBody().getData().get(0);
        assertEquals("EXT001", firstFacility.getLocationDetails().getExternalLocationCode());
        assertEquals(2, firstFacility.getLocationTaskDetailsList().size());
        assertEquals("TASK001", firstFacility.getLocationTaskDetailsList().get(0).getCode());
        assertEquals("TASK002", firstFacility.getLocationTaskDetailsList().get(1).getCode());

        // Verify second facility
        TripFacilityTasksDetailsResponse secondFacility = result.getBody().getData().get(1);
        assertEquals("EXT002", secondFacility.getLocationDetails().getExternalLocationCode());
        assertEquals(1, secondFacility.getLocationTaskDetailsList().size());
        assertEquals("TASK001", secondFacility.getLocationTaskDetailsList().get(0).getCode());

        // Verify service call
        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_WithEmptyResponse_ShouldReturnOkResponseWithEmptyList() {
        // Given
        ListResponse<TripFacilityTasksDetailsResponse> emptyResponse =
                ListResponse.<TripFacilityTasksDetailsResponse>builder()
                        .data(new ArrayList<>())
                        .totalRecords(0L)
                        .build();

        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenReturn(emptyResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals(0, result.getBody().getTotalRecords());
        assertTrue(result.getBody().getData().isEmpty());

        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_WithNullTripCode_ShouldPassNullToService() {
        // Given
        String nullTripCode = null;
        when(tripStopTaskService.getTripTasks(nullTripCode, filterRequest))
                .thenReturn(mockServiceResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(nullTripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(mockServiceResponse, result.getBody());

        verify(tripStopTaskService, times(1)).getTripTasks(nullTripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_WithNullFilterRequest_ShouldPassNullToService() {
        // Given
        FilterRequest<TripTaskListingRequest> nullFilterRequest = null;
        when(tripStopTaskService.getTripTasks(tripCode, nullFilterRequest))
                .thenReturn(mockServiceResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, nullFilterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(mockServiceResponse, result.getBody());

        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, nullFilterRequest);
    }

    @Test
    void getTripTasksByFacility_WithEmptyTripCode_ShouldPassEmptyStringToService() {
        // Given
        String emptyTripCode = "";
        when(tripStopTaskService.getTripTasks(emptyTripCode, filterRequest))
                .thenReturn(mockServiceResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(emptyTripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(mockServiceResponse, result.getBody());

        verify(tripStopTaskService, times(1)).getTripTasks(emptyTripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_WithDifferentPaginationSettings_ShouldPassCorrectRequest() {
        // Given
        Pagination customPagination = new Pagination();
        customPagination.setPageNo(2);
        customPagination.setPageSize(5);

        FilterRequest<TripTaskListingRequest> customFilterRequest = new FilterRequest<>();
        customFilterRequest.setPagination(customPagination);
        customFilterRequest.setFilters(new TripTaskListingRequest());

        when(tripStopTaskService.getTripTasks(tripCode, customFilterRequest))
                .thenReturn(mockServiceResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, customFilterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(mockServiceResponse, result.getBody());

        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, customFilterRequest);
    }

    @Test
    void getTripTasksByFacility_WithSpecialCharactersInTripCode_ShouldHandleCorrectly() {
        // Given
        String specialTripCode = "TRIP-001_TEST@123";
        when(tripStopTaskService.getTripTasks(specialTripCode, filterRequest))
                .thenReturn(mockServiceResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(specialTripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(mockServiceResponse, result.getBody());

        verify(tripStopTaskService, times(1)).getTripTasks(specialTripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_WhenServiceThrowsRuntimeException_ShouldPropagateException() {
        // Given
        RuntimeException serviceException = new RuntimeException("Service error occurred");
        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenThrow(serviceException);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest));

        assertEquals("Service error occurred", exception.getMessage());
        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_WhenServiceThrowsTMSException_ShouldPropagateException() {
        // Given
        TMSException tmsException = new TMSException("INVALID_REQUEST", "Trip not found");
        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenThrow(tmsException);

        // When & Then
        TMSException exception = assertThrows(TMSException.class,
                () -> tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest));

        assertEquals("INVALID_REQUEST", exception.getErrorCode());
        assertEquals("Trip not found", exception.getErrorMessage());
        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_WhenServiceReturnsNull_ShouldReturnOkWithNullBody() {
        // Given
        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenReturn(null);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNull(result.getBody());

        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_WithLargeDataset_ShouldReturnAllData() {
        // Given
        List<TripFacilityTasksDetailsResponse> largeFacilityList = new ArrayList<>();
        for (int i = 0; i < 50; i++) {
            List<TripTasksDetailsResponse> tasks = new ArrayList<>();
            for (int j = 0; j < 5; j++) {
                TripTasksDetailsResponse task = new TripTasksDetailsResponse();
                task.setCode("TASK" + i + "_" + j);
//                task.setTaskName("Task " + i + " - " + j);
                tasks.add(task);
            }

            TripFacilityTasksDetailsResponse facility = TripFacilityTasksDetailsResponse.builder()
                    .locationDetails(LocationDetail.builder()
                            .externalLocationCode("EXT" + String.format("%03d", i))
                            .build())
                    .locationTaskDetailsList(tasks)
                    .build();
            largeFacilityList.add(facility);
        }

        ListResponse<TripFacilityTasksDetailsResponse> largeResponse =
                ListResponse.<TripFacilityTasksDetailsResponse>builder()
                        .data(largeFacilityList)
                        .totalRecords(500L)
                        .build();

        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenReturn(largeResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals(500, result.getBody().getTotalRecords());
        assertEquals(50, result.getBody().getData().size());

        // Verify first and last facilities
        assertEquals("EXT000", result.getBody().getData().get(0).getLocationDetails().getExternalLocationCode());
        assertEquals("EXT049", result.getBody().getData().get(49).getLocationDetails().getExternalLocationCode());
        assertEquals(5, result.getBody().getData().get(0).getLocationTaskDetailsList().size());

        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_WithFilterRequestHavingNullPagination_ShouldHandleCorrectly() {
        // Given
        FilterRequest<TripTaskListingRequest> requestWithNullPagination = new FilterRequest<>();
        requestWithNullPagination.setPagination(null);
        requestWithNullPagination.setFilters(new TripTaskListingRequest());

        when(tripStopTaskService.getTripTasks(tripCode, requestWithNullPagination))
                .thenReturn(mockServiceResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, requestWithNullPagination);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(mockServiceResponse, result.getBody());

        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, requestWithNullPagination);
    }

    @Test
    void getTripTasksByFacility_WithFilterRequestHavingNullFilter_ShouldHandleCorrectly() {
        // Given
        FilterRequest<TripTaskListingRequest> requestWithNullFilter = new FilterRequest<>();
        requestWithNullFilter.setPagination(pagination);
        requestWithNullFilter.setFilters(null);

        when(tripStopTaskService.getTripTasks(tripCode, requestWithNullFilter))
                .thenReturn(mockServiceResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, requestWithNullFilter);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertEquals(mockServiceResponse, result.getBody());

        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, requestWithNullFilter);
    }

    @Test
    void getTripTasksByFacility_ShouldAlwaysReturnHttpStatusOk_WhenServiceReturnsSuccessfully() {
        // Given - Different service responses
        ListResponse<TripFacilityTasksDetailsResponse> emptyResponse =
                ListResponse.<TripFacilityTasksDetailsResponse>builder()
                        .data(new ArrayList<>())
                        .totalRecords(0L)
                        .build();

        ListResponse<TripFacilityTasksDetailsResponse> singleItemResponse =
                ListResponse.<TripFacilityTasksDetailsResponse>builder()
                        .data(Arrays.asList(mockResponseList.get(0)))
                        .totalRecords(1L)
                        .build();

        // Test with empty response
        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenReturn(emptyResponse);

        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result1 =
                tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest);
        assertEquals(HttpStatus.OK, result1.getStatusCode());

        // Test with single item response
        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenReturn(singleItemResponse);

        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result2 =
                tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest);
        assertEquals(HttpStatus.OK, result2.getStatusCode());

        // Test with normal response
        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenReturn(mockServiceResponse);

        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result3 =
                tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest);
        assertEquals(HttpStatus.OK, result3.getStatusCode());

        verify(tripStopTaskService, times(3)).getTripTasks(tripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_ShouldNotModifyServiceResponse() {
        // Given
        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenReturn(mockServiceResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertSame(mockServiceResponse, result.getBody()); // Should be the exact same object reference

        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, filterRequest);
    }

    @Test
    void getTripTasksByFacility_ShouldCallServiceExactlyOnce_ForEachRequest() {
        // Given
        when(tripStopTaskService.getTripTasks(anyString(), any(FilterRequest.class)))
                .thenReturn(mockServiceResponse);

        // When - Multiple calls
        tripTaskApiController.getTripTasksByFacility("TRIP001", filterRequest);
        tripTaskApiController.getTripTasksByFacility("TRIP002", filterRequest);
        tripTaskApiController.getTripTasksByFacility("TRIP003", filterRequest);

        // Then
        verify(tripStopTaskService, times(1)).getTripTasks("TRIP001", filterRequest);
        verify(tripStopTaskService, times(1)).getTripTasks("TRIP002", filterRequest);
        verify(tripStopTaskService, times(1)).getTripTasks("TRIP003", filterRequest);
        verify(tripStopTaskService, times(3)).getTripTasks(anyString(), any(FilterRequest.class));
    }

    @Test
    void getTripTasksByFacility_WithZeroTotalRecords_ShouldReturnValidResponse() {
        // Given
        ListResponse<TripFacilityTasksDetailsResponse> zeroRecordsResponse =
                ListResponse.<TripFacilityTasksDetailsResponse>builder()
                        .data(new ArrayList<>())
                        .totalRecords(0L)
                        .build();

        when(tripStopTaskService.getTripTasks(tripCode, filterRequest))
                .thenReturn(zeroRecordsResponse);

        // When
        ResponseEntity<ListResponse<TripFacilityTasksDetailsResponse>> result =
                tripTaskApiController.getTripTasksByFacility(tripCode, filterRequest);

        // Then
        assertNotNull(result);
        assertEquals(HttpStatus.OK, result.getStatusCode());
        assertNotNull(result.getBody());
        assertEquals(0, result.getBody().getTotalRecords());
        assertTrue(result.getBody().getData().isEmpty());

        verify(tripStopTaskService, times(1)).getTripTasks(tripCode, filterRequest);
    }
}