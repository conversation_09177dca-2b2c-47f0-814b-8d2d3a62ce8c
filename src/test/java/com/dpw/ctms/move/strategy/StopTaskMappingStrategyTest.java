package com.dpw.ctms.move.strategy;

import com.dpw.ctms.move.dto.ParamValueStopDTO;
import com.dpw.ctms.move.entity.Stop;
import com.dpw.ctms.move.entity.StopTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.StopTaskEvent;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.strategy.entitytaskmapping.StopTaskMappingStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class StopTaskMappingStrategyTest {

    private StopTaskMappingStrategy stopTaskMappingStrategy;

    @BeforeEach
    void setUp() {
        stopTaskMappingStrategy = new StopTaskMappingStrategy();
    }

    @Test
    void shouldMapStopTaskToMatchingStopAndTask() {
        // given
        ParamValueStopDTO paramValueStopDTO = ParamValueStopDTO.builder()
                .externalLocationCode("LOC-1")
                .sequence(1)
                .taskEvent("DEPARTURE")
                .build();

        Stop stop = Stop.builder()
                .externalLocationCode("LOC-1")
                .sequence(1)
                .stopTasks(new ArrayList<>()) // ensure this is initialized
                .build();

        Trip trip = new Trip();
        trip.setStops(new HashSet<>(List.of(stop)));

        Task task = new Task();

        // when
        stopTaskMappingStrategy.mapEntityTask(paramValueStopDTO, trip, task);

        // then
        StopTask stopTask = task.getStopTask();
        assertNotNull(stopTask, "StopTask should be set in Task");
        assertEquals(stop, stopTask.getStop(), "StopTask should reference the correct Stop");
        assertEquals(task, stopTask.getTask(), "StopTask should reference the correct Task");
        assertEquals(StopTaskEvent.DEPARTURE, stopTask.getTaskEvent(), "TaskEvent should be correctly mapped");
        assertTrue(stop.getStopTasks().contains(stopTask), "Stop should contain the mapped StopTask");
    }

    @Test
    void shouldNotMapStopTaskIfNoMatchingStop() {
        // given
        ParamValueStopDTO paramValueStopDTO = ParamValueStopDTO.builder()
                .externalLocationCode("LOC-2")
                .sequence(2)
                .taskEvent("ARRIVAL")
                .build();

        Stop stop = Stop.builder()
                .externalLocationCode("LOC-1")
                .sequence(1)
                .stopTasks(new ArrayList<>())
                .build();

        Trip trip = new Trip();
        trip.setStops(new HashSet<>(List.of(stop)));

        Task task = new Task();

        // when
        stopTaskMappingStrategy.mapEntityTask(paramValueStopDTO, trip, task);

        // then
        assertNull(task.getStopTask(), "Task should not have a StopTask if no matching stop");
        assertTrue(stop.getStopTasks().isEmpty(), "Stop should not contain any StopTask if not matching");
    }

    @Test
    void shouldReturnTaskParamTypeSTOP() {
        // when
        TaskParamType type = stopTaskMappingStrategy.getParamType();

        // then
        assertEquals(TaskParamType.STOP, type);
    }
}

