package com.dpw.ctms.move.strategy;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.ShipmentTask;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.Trip;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.strategy.entitytaskmapping.ShipmentTaskMappingStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ShipmentTaskMappingStrategyTest {

    private ShipmentTaskMappingStrategy shipmentTaskMappingStrategy;

    @BeforeEach
    void setUp() {
        shipmentTaskMappingStrategy = new ShipmentTaskMappingStrategy();
    }

    @Test
    void shouldMapShipmentTaskToMatchingShipmentAndTask() {
        // given
        ParamValueShipmentDTO dto = ParamValueShipmentDTO.builder()
                .code("SHIP-001")
                .build();

        Shipment shipment = Shipment.builder()
                .code("SHIP-001")
                .shipmentTasks(new ArrayList<>()) // ensure initialized
                .build();

        Trip trip = new Trip();
        trip.setShipments(new HashSet<>(List.of(shipment)));

        Task task = new Task();

        // when
        shipmentTaskMappingStrategy.mapEntityTask(dto, trip, task);

        // then
        ShipmentTask shipmentTask = task.getShipmentTask();
        assertNotNull(shipmentTask, "ShipmentTask should be set in Task");
        assertEquals(task, shipmentTask.getTask(), "ShipmentTask should link back to Task");
        assertEquals(shipment, shipmentTask.getShipment(), "ShipmentTask should link to correct Shipment");
        assertTrue(shipment.getShipmentTasks().contains(shipmentTask), "Shipment should contain the created ShipmentTask");
    }

    @Test
    void shouldNotMapShipmentTaskIfNoMatchingShipment() {
        // given
        ParamValueShipmentDTO dto = ParamValueShipmentDTO.builder()
                .code("SHIP-002")
                .build();

        Shipment shipment = Shipment.builder()
                .code("SHIP-001")
                .shipmentTasks(new ArrayList<>())
                .build();
        Trip trip = new Trip();
        trip.setShipments(new HashSet<>(List.of(shipment)));


        Task task = new Task();

        // when
        shipmentTaskMappingStrategy.mapEntityTask(dto, trip, task);

        // then
        assertNull(task.getShipmentTask(), "Task should not have ShipmentTask if no match found");
        assertTrue(shipment.getShipmentTasks().isEmpty(), "No ShipmentTask should be added if no match found");
    }

    @Test
    void shouldReturnTaskParamTypeSHIPMENT() {
        assertEquals(TaskParamType.SHIPMENT, shipmentTaskMappingStrategy.getParamType());
    }
}
