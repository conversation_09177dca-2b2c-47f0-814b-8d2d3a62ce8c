package com.dpw.ctms.move.service;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.dto.ShipmentDetailsDTO;
import com.dpw.ctms.move.entity.Shipment;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.service.impl.ConsignmentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ConsignmentServiceTest {

    @Mock
    private IShipmentService shipmentService;

    @Mock
    private ITaskParamService taskParamService;

    @InjectMocks
    private ConsignmentService consignmentService;

    private Task task1;
    private Task task2;
    private Task task3;
    private Shipment shipment1;
    private Shipment shipment2;
    private ParamValueShipmentDTO paramValue1;
    private ParamValueShipmentDTO paramValue2;

    @BeforeEach
    void setUp() {
        // Setup tasks
        task1 = new Task();
        task1.setCode("TASK_001");

        task2 = new Task();
        task2.setCode("TASK_002");

        task3 = new Task();
        task3.setCode("TASK_003");

        // Setup shipments
        shipment1 = new Shipment();
        shipment1.setCode("SHIP_001");
        shipment1.setExternalCustomerOrderId("ORDER_001");
        shipment1.setExternalConsignmentId("CONSIGN_001");

        shipment2 = new Shipment();
        shipment2.setCode("SHIP_002");
        shipment2.setExternalCustomerOrderId("ORDER_002");
        shipment2.setExternalConsignmentId("CONSIGN_002");

        // Setup param values
        paramValue1 = new ParamValueShipmentDTO();
        paramValue1.setCode("SHIP_001");

        paramValue2 = new ParamValueShipmentDTO();
        paramValue2.setCode("SHIP_002");
    }

    @Test
    void testGetShipmentDetailsFromTask_Success() {
        // Given
        List<Task> tasks = Arrays.asList(task1, task2);
        List<Shipment> shipments = Arrays.asList(shipment1, shipment2);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.of(paramValue2));
        when(shipmentService.getAllByCodes(anySet())).thenReturn(shipments);

        // When
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        ShipmentDetailsDTO details1 = result.get("SHIP_001");
        assertNotNull(details1);
        assertEquals("SHIP_001", details1.getShipmentCode());
        assertEquals("ORDER_001", details1.getExternalCustomerOrderId());
        assertEquals("CONSIGN_001", details1.getExternalConsignmentId());

        ShipmentDetailsDTO details2 = result.get("SHIP_002");
        assertNotNull(details2);
        assertEquals("SHIP_002", details2.getShipmentCode());
        assertEquals("ORDER_002", details2.getExternalCustomerOrderId());
        assertEquals("CONSIGN_002", details2.getExternalConsignmentId());

        verify(taskParamService, times(1)).getShipmentCode(task1);
        verify(taskParamService, times(1)).getShipmentCode(task2);
        verify(shipmentService, times(1)).getAllByCodes(Set.of("SHIP_001", "SHIP_002"));
    }

    @Test
    void testGetShipmentDetailsFromTask_EmptyTaskList() {
        // Given
        List<Task> tasks = Collections.emptyList();
        List<Shipment> shipments = Collections.emptyList();

        when(shipmentService.getAllByCodes(anySet())).thenReturn(shipments);

        // When
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(taskParamService, never()).getShipmentCode(any());
        verify(shipmentService, times(1)).getAllByCodes(Collections.emptySet());
    }

    @Test
    void testGetShipmentDetailsFromTask_TasksWithNoShipmentCode() {
        // Given
        List<Task> tasks = Arrays.asList(task1, task2);
        List<Shipment> shipments = Collections.emptyList();

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.empty());
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.empty());
        when(shipmentService.getAllByCodes(anySet())).thenReturn(shipments);

        // When
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(taskParamService, times(1)).getShipmentCode(task1);
        verify(taskParamService, times(1)).getShipmentCode(task2);
        verify(shipmentService, times(1)).getAllByCodes(Collections.emptySet());
    }

    @Test
    void testGetShipmentDetailsFromTask_MixedTasksWithAndWithoutShipmentCode() {
        // Given
        List<Task> tasks = Arrays.asList(task1, task2, task3);
        List<Shipment> shipments = Arrays.asList(shipment1);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.empty());
        when(taskParamService.getShipmentCode(task3)).thenReturn(Optional.empty());
        when(shipmentService.getAllByCodes(anySet())).thenReturn(shipments);

        // When
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        ShipmentDetailsDTO details1 = result.get("SHIP_001");
        assertNotNull(details1);
        assertEquals("SHIP_001", details1.getShipmentCode());
        assertEquals("ORDER_001", details1.getExternalCustomerOrderId());
        assertEquals("CONSIGN_001", details1.getExternalConsignmentId());

        verify(taskParamService, times(1)).getShipmentCode(task1);
        verify(taskParamService, times(1)).getShipmentCode(task2);
        verify(taskParamService, times(1)).getShipmentCode(task3);
        verify(shipmentService, times(1)).getAllByCodes(Set.of("SHIP_001"));
    }

    @Test
    void testGetShipmentDetailsFromTask_DuplicateShipmentCodes() {
        // Given
        List<Task> tasks = Arrays.asList(task1, task2);
        List<Shipment> shipments = Arrays.asList(shipment1);

        // Both tasks point to the same shipment
        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.of(paramValue1));
        when(shipmentService.getAllByCodes(anySet())).thenReturn(shipments);

        // When
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        ShipmentDetailsDTO details1 = result.get("SHIP_001");
        assertNotNull(details1);
        assertEquals("SHIP_001", details1.getShipmentCode());
        assertEquals("ORDER_001", details1.getExternalCustomerOrderId());
        assertEquals("CONSIGN_001", details1.getExternalConsignmentId());

        verify(taskParamService, times(1)).getShipmentCode(task1);
        verify(taskParamService, times(1)).getShipmentCode(task2);
        verify(shipmentService, times(1)).getAllByCodes(Set.of("SHIP_001"));
    }

    @Test
    void testGetShipmentDetailsFromTask_ShipmentServiceReturnsEmpty() {
        // Given
        List<Task> tasks = Arrays.asList(task1, task2);
        List<Shipment> shipments = Collections.emptyList();

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(taskParamService.getShipmentCode(task2)).thenReturn(Optional.of(paramValue2));
        when(shipmentService.getAllByCodes(anySet())).thenReturn(shipments);

        // When
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(taskParamService, times(1)).getShipmentCode(task1);
        verify(taskParamService, times(1)).getShipmentCode(task2);
        verify(shipmentService, times(1)).getAllByCodes(Set.of("SHIP_001", "SHIP_002"));
    }

    @Test
    void testGetShipmentDetailsFromTask_NullTaskList() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            consignmentService.getShipmentDetailsFromTask(null);
        });
    }

    @Test
    void testGetShipmentDetailsFromTask_TaskParamServiceThrowsException() {
        // Given
        List<Task> tasks = Arrays.asList(task1);

        when(taskParamService.getShipmentCode(task1)).thenThrow(new RuntimeException("Service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            consignmentService.getShipmentDetailsFromTask(tasks);
        });

        verify(taskParamService, times(1)).getShipmentCode(task1);
        verify(shipmentService, never()).getAllByCodes(anySet());
    }

    @Test
    void testGetShipmentDetailsFromTask_ShipmentServiceThrowsException() {
        // Given
        List<Task> tasks = Arrays.asList(task1);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(shipmentService.getAllByCodes(anySet())).thenThrow(new RuntimeException("Service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            consignmentService.getShipmentDetailsFromTask(tasks);
        });

        verify(taskParamService, times(1)).getShipmentCode(task1);
        verify(shipmentService, times(1)).getAllByCodes(Set.of("SHIP_001"));
    }

    @Test
    void testGetShipmentDetailsFromTask_ShipmentWithNullValues() {
        // Given
        List<Task> tasks = Arrays.asList(task1);
        Shipment shipmentWithNulls = new Shipment();
        shipmentWithNulls.setCode("SHIP_001");
        shipmentWithNulls.setExternalCustomerOrderId(null);
        shipmentWithNulls.setExternalConsignmentId(null);
        List<Shipment> shipments = Arrays.asList(shipmentWithNulls);

        when(taskParamService.getShipmentCode(task1)).thenReturn(Optional.of(paramValue1));
        when(shipmentService.getAllByCodes(anySet())).thenReturn(shipments);

        // When
        Map<String, ShipmentDetailsDTO> result = consignmentService.getShipmentDetailsFromTask(tasks);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        ShipmentDetailsDTO details = result.get("SHIP_001");
        assertNotNull(details);
        assertEquals("SHIP_001", details.getShipmentCode());
        assertNull(details.getExternalCustomerOrderId());
        assertNull(details.getExternalConsignmentId());

        verify(taskParamService, times(1)).getShipmentCode(task1);
        verify(shipmentService, times(1)).getAllByCodes(Set.of("SHIP_001"));
    }
}