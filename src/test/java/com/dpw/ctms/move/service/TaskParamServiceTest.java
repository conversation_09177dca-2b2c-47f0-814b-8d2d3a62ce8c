package com.dpw.ctms.move.service;

import com.dpw.ctms.move.dto.ParamValueShipmentDTO;
import com.dpw.ctms.move.entity.Task;
import com.dpw.ctms.move.entity.TaskParam;
import com.dpw.ctms.move.enums.TaskParamType;
import com.dpw.ctms.move.service.impl.TaskParamService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class TaskParamServiceTest {

    @InjectMocks
    private TaskParamService taskParamService;

    private Task task;
    private TaskParam shipmentTaskParam;
    private TaskParam nonShipmentTaskParam;
    private ParamValueShipmentDTO shipmentDTO;

    @BeforeEach
    void setUp() {
        // Setup task
        task = new Task();
        task.setCode("TASK001");

        // Setup shipment DTO
        shipmentDTO = new ParamValueShipmentDTO();
        shipmentDTO.setCode("SHIP001");

        // Setup shipment task param
        shipmentTaskParam = new TaskParam();
        shipmentTaskParam.setParamName(TaskParamType.SHIPMENT);
        shipmentTaskParam.setParamValue(shipmentDTO);

        // Setup non-shipment task param
        nonShipmentTaskParam = new TaskParam();
        nonShipmentTaskParam.setParamName(TaskParamType.valueOf(TaskParamType.SHIPMENT.name())); // Assuming there's another enum value
//        nonShipmentTaskParam.setParamValue("some other value");
    }

    @Test
    void getShipmentCode_WithValidShipmentParam_ShouldReturnShipmentDTO() {
        // Given
        task.setTaskParams(Arrays.asList(shipmentTaskParam));

        // When
        Optional<ParamValueShipmentDTO> result = taskParamService.getShipmentCode(task);

        // Then
        assertTrue(result.isPresent());
        assertEquals(shipmentDTO, result.get());
        assertEquals("SHIP001", result.get().getCode());
    }

    @Test
    void getShipmentCode_WithMultipleParamsIncludingShipment_ShouldReturnShipmentDTO() {
        // Given
        task.setTaskParams(Arrays.asList(nonShipmentTaskParam, shipmentTaskParam));

        // When
        Optional<ParamValueShipmentDTO> result = taskParamService.getShipmentCode(task);

        // Then
        assertTrue(result.isPresent());
        assertEquals(shipmentDTO, result.get());
        assertEquals("SHIP001", result.get().getCode());
    }

    @Test
    void getShipmentCode_WithNoShipmentParam_ShouldReturnEmpty() {
        // Given
        task.setTaskParams(Arrays.asList(nonShipmentTaskParam));

        // When
        Optional<ParamValueShipmentDTO> result = taskParamService.getShipmentCode(task);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void getShipmentCode_WithEmptyTaskParams_ShouldReturnEmpty() {
        // Given
        task.setTaskParams(Collections.emptyList());

        // When
        Optional<ParamValueShipmentDTO> result = taskParamService.getShipmentCode(task);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void getShipmentCode_WithNullTaskParams_ShouldThrowException() {
        // Given
        task.setTaskParams(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            taskParamService.getShipmentCode(task);
        });
    }

    @Test
    void getShipmentCode_WithShipmentParamButNullValue_ShouldReturnEmpty() {
        // Given
        TaskParam nullValueParam = new TaskParam();
        nullValueParam.setParamName(TaskParamType.SHIPMENT);
        nullValueParam.setParamValue(null);

        task.setTaskParams(Arrays.asList(nullValueParam));

        // When
        Optional<ParamValueShipmentDTO> result = taskParamService.getShipmentCode(task);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void getShipmentCode_WithMultipleShipmentParams_ShouldReturnFirst() {
        // Given
        ParamValueShipmentDTO shipmentDTO2 = new ParamValueShipmentDTO();
        shipmentDTO2.setCode("SHIP002");

        TaskParam shipmentTaskParam2 = new TaskParam();
        shipmentTaskParam2.setParamName(TaskParamType.SHIPMENT);
        shipmentTaskParam2.setParamValue(shipmentDTO2);

        task.setTaskParams(Arrays.asList(shipmentTaskParam, shipmentTaskParam2));

        // When
        Optional<ParamValueShipmentDTO> result = taskParamService.getShipmentCode(task);

        // Then
        assertTrue(result.isPresent());
        assertEquals(shipmentDTO, result.get()); // Should return the first one
        assertEquals("SHIP001", result.get().getCode());
    }

    @Test
    void getShipmentCode_WithNullTask_ShouldThrowException() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            taskParamService.getShipmentCode(null);
        });
    }

    @Test
    void getShipmentCode_WithTaskParamHavingNullParamName_ShouldSkipParam() {
        // Given
        TaskParam nullParamNameParam = new TaskParam();
        nullParamNameParam.setParamName(null);
        nullParamNameParam.setParamValue(shipmentDTO);

        task.setTaskParams(Arrays.asList(nullParamNameParam, shipmentTaskParam));

        // When
        Optional<ParamValueShipmentDTO> result = taskParamService.getShipmentCode(task);

        // Then
        assertTrue(result.isPresent());
        assertEquals(shipmentDTO, result.get());
        assertEquals("SHIP001", result.get().getCode());
    }

    @Test
    void getShipmentCode_WithShipmentDTOHavingNullCode_ShouldStillReturnDTO() {
        // Given
        ParamValueShipmentDTO shipmentDTOWithNullCode = new ParamValueShipmentDTO();
        shipmentDTOWithNullCode.setCode(null);

        TaskParam paramWithNullCode = new TaskParam();
        paramWithNullCode.setParamName(TaskParamType.SHIPMENT);
        paramWithNullCode.setParamValue(shipmentDTOWithNullCode);

        task.setTaskParams(Arrays.asList(paramWithNullCode));

        // When
        Optional<ParamValueShipmentDTO> result = taskParamService.getShipmentCode(task);

        // Then
        assertTrue(result.isPresent());
        assertEquals(shipmentDTOWithNullCode, result.get());
        assertNull(result.get().getCode());
    }
}